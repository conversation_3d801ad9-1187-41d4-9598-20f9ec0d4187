from django.db import models
from django.utils.translation import gettext_lazy as _
from app.utils.uploads import get_upload_to


class FileType(models.IntegerChoices):
    IMAGE = 1, _("Image")
    OTHER = 2, _("Other")


class UploadedFile(models.Model):
    file = models.FileField(upload_to=get_upload_to)
    file_name = models.CharField(max_length=255)
    file_size = models.BigIntegerField()
    file_type = models.PositiveSmallIntegerField(
        choices=FileType.choices,
        default=FileType.OTHER,
    )
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
            if not self.file_name:
                self.file_name = self.file.name
            self.file_type = self.get_file_type()
        super().save(*args, **kwargs)

    def get_file_type(self):
        """Determine the file type based on the extension."""
        if self.file_name.lower().endswith((".jpg", ".jpeg", ".png", ".gif")):
            return FileType.IMAGE

        return FileType.OTHER
