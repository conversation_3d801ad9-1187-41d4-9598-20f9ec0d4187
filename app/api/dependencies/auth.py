"""
Authentication dependencies for the API.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from jose import JWTError, jwt

from ..schemas.auth import TokenData

# JWT settings (should match the ones in auth.py)
SECRET_KEY = "your-secret-key-here"  # In production, use environment variable
ALGORITHM = "HS256"

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")


async def get_current_user(token: str = Depends(oauth2_scheme)) -> TokenData:
    """Get current user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JW<PERSON>rror:
        raise credentials_exception
    
    return token_data 