# views.py

from django.shortcuts import render, get_object_or_404, redirect
from app.forms.profile import ProfilePictureForm
from app.models import Profile

def upload_profile_picture(request):
    profile = get_object_or_404(Profile, user=request.user)
    if request.method == "POST":
        form = ProfilePictureForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            return render(request, "upload_profile_picture.html", {
                "form": form,
                "url": profile.profile_picture.url,
                "success": True,
            })
    else:
        form = ProfilePictureForm(instance=profile)
    
    return render(request, "upload_profile_picture.html", {"form": form})
