# Smart Plate Backend

Backend API for Smart Plate – handles food recognition, calorie estimation, and meal logging

## Development setup

### Prerequisites

* Python 3.11 or higher
* uv installed (`pip install uv` or follow [installation guide](https://docs.astral.sh/uv/getting-started/installation/))

### Local Development Steps

1. Clone the repo and navigate inside:
   ```bash
   <NAME_EMAIL>:testpress/smartplate_backend.git
   cd smartplate_backend
   ```

2. Install dependencies:
   ```bash
   # Install main dependencies
   uv sync

   # Install development dependencies
   uv sync --extra dev
   ```

3. Activate virtual environment:
   ```bash
   source .venv/bin/activate
   ```

4. Run migrations:
   ```bash
   uv run python manage.py migrate
   ```

5. Start the development server:
   ```bash
   uv run uvicorn config.asgi:application --reload
   ```

   The API documentation will be available at: http://localhost:8000/api/docs


## License

Smart Plate Backend is licensed under the MIT license.
